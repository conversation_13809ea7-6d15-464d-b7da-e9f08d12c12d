from __future__ import print_function

import argparse
import asyncio
import os
import shutil
import signal
import subprocess
import sys
import tempfile
import time
from collections import defaultdict
from concurrent.futures import Thread<PERSON>oolExecutor
from pathlib import Path

import requests
import tornado.web
from loguru import logger
from tornado import gen, locks
from tornado.concurrent import run_on_executor
from tornado.httpclient import AsyncHT<PERSON>Client
from tornado.ioloop import <PERSON><PERSON><PERSON>

import heartbeat
from idb import WDADevice, track_devices
from log import log_request
from utils import current_ip
from typing import Dict, Optional, Tuple

basedir: Path = Path(__file__).parent
# basename: str = "ios-provider"
listen_on: str
tunnel_proc: Optional[subprocess.Popen] = None
socat_proc: Optional[subprocess.Popen] = None
udid2devices: Dict[str, WDADevice] = {}
hbc: Optional[heartbeat.HeartbeatConnection] = None


class CorsMixin(object):
    CORS_ORIGIN = "*"
    CORS_METHODS = "GET,POST,OPTIONS"
    CORS_CREDENTIALS = True
    CORS_HEADERS = "x-requested-with,authorization"

    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", self.CORS_ORIGIN)
        self.set_header("Access-Control-Allow-Headers", self.CORS_HEADERS)
        self.set_header("Access-Control-Allow-Methods", self.CORS_METHODS)

    def options(self):
        # no body
        self.set_status(204)
        self.finish()


class MainHandler(tornado.web.RequestHandler):
    @gen.coroutine
    def get(self):
        yield gen.sleep(0.5)
        self.write("Hello, world")


class ProxyTesterHomeHandler(tornado.web.RequestHandler):
    @gen.coroutine
    def get(self):
        body = yield self.get_testerhome()
        self.write(body)

    @gen.coroutine
    def get_testerhome(self):
        http_client = AsyncHTTPClient()
        response = yield http_client.fetch("https://testerhome.com/")
        raise gen.Return(response.body)


class AppInstallHandler(CorsMixin, tornado.web.RequestHandler):
    executor = ThreadPoolExecutor(4)

    @run_on_executor(executor="executor")
    def app_install(self, udid: str, url: str):
        try:
            r = requests.get(url, stream=True)
            if r.status_code != 200:
                return {"success": False, "description": r.reason}
        except Exception as e:
            return {"success": False, "description": str(e)}

        # temporary file
        logger.debug("{} app-install from {}", udid, url)
        tmp_file = tempfile.NamedTemporaryFile(
            suffix=".ipa", prefix="tmpfile-", dir=os.getcwd()
        )
        try:
            ipa_path = tmp_file.name
            logger.debug("{} temp ipa path: {}", udid, ipa_path)

            content_length = int(r.headers.get("content-length", 0))
            if content_length:
                for chunk in r.iter_content(chunk_size=40960):
                    tmp_file.write(chunk)
            else:
                shutil.copyfileobj(r.raw, tmp_file)

            p = subprocess.Popen(
                ["ideviceinstaller", "-u", udid, "-i", ipa_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
            )
            output = ""
            for line in p.stdout:
                line = line.decode("utf-8")
                logger.debug("{} -- {}", udid, line.strip())
                output += line
            success = "Complete" in output
            exit_code = p.wait()

            if not success:
                return {"success": False, "description": output}
            return {
                "success": success,
                # "bundleId": bundle_id,
                "return": exit_code,
                "output": output,
            }
        except Exception as e:
            return {"success": False, "status": 500, "description": str(e)}
        finally:
            tmp_file.close()

    @gen.coroutine
    def post(self):
        udid = self.get_argument("udid")
        url = self.get_argument("url")
        device = udid2devices[udid]
        ret = yield self.app_install(device.udid, url)
        if not ret["success"]:
            self.set_status(ret.get("status", 400))  # default bad request
        self.write(ret)


class ReleaseDeviceHandler(tornado.web.RequestHandler):
    @staticmethod
    async def release(_device: WDADevice):
        started_at = time.time()
        try:
            logger.info(
                "start to release the device, udid: {}, port: {}",
                _device.udid,
                _device.public_port,
            )

            # await _device.restart_wda()
            _device.restart_wda_proxy()
            await _device.wda_healthcheck()

            logger.info(
                "finish to release the device, udid: {}, port: {}, elapsed: {:.2f}ms",
                _device.udid,
                _device.public_port,
                (time.time() - started_at) * 1000,
            )
        except Exception as e:
            logger.error(
                "got an error while releasing the device, udid: {}, port: {}, elapsed: {:.2f}ms, error: {} {}",
                _device.udid,
                _device.public_port,
                (time.time() - started_at) * 1000,
                type(e),
                e,
            )

    async def post(self, udid: Optional[str] = None):
        udid = udid or self.get_argument("udid", None)
        device = udid2devices.get(udid)
        try:
            if not device:
                raise Exception("the device not found, udid: {}", udid)

            logger.info(
                "request to release the device, udid: {}, port: {}",
                device.udid,
                device.public_port,
            )
            IOLoop.current().add_callback(self.release, device)

            self.write(
                {
                    "success": True,
                    "description": f"releasing the device, udid: {udid}",
                }
            )
        except Exception as e:
            logger.error(
                "got an error while releasing the device, udid: {}, error: {} {}",
                udid,
                type(e),
                e,
            )
            self.set_status(400)
            self.write(
                {
                    "success": False,
                    "description": f"release the device failed, udid: {udid}, error: {e}",
                }
            )


class CoolingHandler(tornado.web.RequestHandler):
    """reset device to clean state"""

    async def post(self, udid=None):
        udid = udid or self.get_argument("udid", None)
        assert udid
        device = udid2devices.get(udid)
        try:
            if not device:
                raise Exception("Device not found")

            device.restart_wda_proxy()  # change wda public port
            wda_url = "http://{}:{}".format(current_ip(), device.public_port)
            await device.wda_healthcheck()
            await hbc.device_update(
                {
                    "udid": udid,
                    "colding": False,
                    "provider": {
                        "wdaUrl": wda_url,
                    },
                }
            )
            self.write({"success": True, "description": "Device successfully colded"})
        except Exception as e:
            logger.warning("colding procedure got error: {}", e)
            self.set_status(400)  # bad request
            self.write({"success": False, "description": "udid: %s not found" % udid})


def make_app(**settings) -> tornado.web.Application:
    settings["template_path"] = "templates"
    settings["static_path"] = "static"
    settings["cookie_secret"] = os.environ.get("SECRET", "SECRET:_")
    settings["login_url"] = "/login"
    settings["log_function"] = log_request
    return tornado.web.Application(
        [
            (r"/", MainHandler),
            (r"/testerhome", ProxyTesterHomeHandler),
            (r"/devices/([^/]+)/cold", CoolingHandler),
            (r"/devices/([^/]+)/app/install", AppInstallHandler),
            (r"/devices/([^/]+)/release", ReleaseDeviceHandler),
            (r"/cold", CoolingHandler),
            (r"/app/install", AppInstallHandler),
            (r"/release", ReleaseDeviceHandler),
        ],
        **settings,
    )


def check_tunnel_server() -> Tuple[Optional[int], bool]:
    cmd = ["pgrep", "-f", "go-ios.*tunnel start.*"]
    cmd_str = " ".join(cmd)
    try:
        p = subprocess.run(
            cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True
        )
        pid = int(p.stdout.decode(encoding="utf-8").strip())
        logger.info("check tunnel server, cmd: `{}`, pid: {}", cmd_str, pid)
        return pid, True
    except subprocess.CalledProcessError as e:
        logger.info("check tunnel server, cmd: `{}`, ret: {}, err: {}", cmd_str, e.returncode, e.stderr)
        return None, False
    except Exception as e:
        logger.error("check tunnel server, cmd: `{}`, err: {}", cmd_str, e)
        return None, False


def start_tunnel_server():
    pid, exists = check_tunnel_server()
    if pid and exists:
        logger.info("try to kill the existing tunnel server, pid: {}", pid)
        os.kill(pid, signal.SIGTERM)

    global tunnel_proc

    cmd = [
        str(basedir / "bin" / "go-ios.darwin"),
        "--socket", ":27015",
        "--tunnel-info-host", "0.0.0.0",
        "--userspace-host", "0.0.0.0",
        "tunnel", "start", "--userspace"
    ]
    cmd_str = " ".join(cmd)
    tunnel_proc = subprocess.Popen(
        cmd, stdout=subprocess.DEVNULL, stderr=subprocess.PIPE
    )
    if not tunnel_proc.pid:
        raise Exception(
            f"failed to start tunnel server, cmd: `{cmd_str}`, ret: {tunnel_proc.returncode}, err: {tunnel_proc.stderr.readlines()[0].decode(encoding='utf-8')}"
        )
    tunnel_proc.stderr = None
    logger.info("start tunnel server, pid: {}", tunnel_proc.pid)


def stop_tunnel_server():
    if tunnel_proc:
        pid = tunnel_proc.pid
        tunnel_proc.terminate()
        tunnel_proc.wait()
        logger.info("stop tunnel server, pid {}", pid)


def check_socat() -> Tuple[Optional[int], bool]:
    cmd = ["pgrep", "-f", "socat.*27015.*"]
    cmd_str = " ".join(cmd)
    try:
        p = subprocess.run(
            cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True
        )
        pid = int(p.stdout.decode(encoding="utf-8").strip())
        logger.info("check socat, cmd: `{}`, pid: {}", cmd_str, pid)
        return pid, True
    except subprocess.CalledProcessError as e:
        logger.info("check socat, cmd: `{}`, ret: {}, err: {}", cmd_str, e.returncode, e.stderr)
        return None, False
    except Exception as e:
        logger.error("check socat, cmd: `{}`, err: {}", cmd_str, e)
        return None, False


def start_socat():
    pid, exists = check_socat()
    if pid and exists:
        logger.info("try to kill the existing socat process, pid: {}", pid)
        os.kill(pid, signal.SIGTERM)

    global socat_proc

    cmd = [
        "socat",
        "-d2",
        "-lf", str(basedir / "logs" / "socat.log"),
        "-T", "120",
        "TCP-LISTEN:27015,fork,reuseaddr", "UNIX:/var/run/usbmuxd"
    ]
    cmd_str = " ".join(cmd)
    socat_proc = subprocess.Popen(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.PIPE)
    if not socat_proc.pid:
        raise Exception(
            f"failed to start the `socat` process, cmd: `{cmd_str}`, ret: {socat_proc.returncode}, err: {socat_proc.stderr.readlines()[0].decode(encoding='utf-8')}"
        )
    socat_proc.stderr = None
    logger.info("start the `socat` process, pid: {}", socat_proc.pid)


def stop_socat():
    if socat_proc:
        pid = socat_proc.pid
        socat_proc.terminate()
        socat_proc.wait()
        logger.info("stop the `socat` process, pid: {}", pid)


async def _device_callback(device: WDADevice, status: str, info: Optional[dict] = None):
    """monitor device status"""
    logger.info("{} device status: {}", device, status)

    if status == device.PREPARING:
        await hbc.device_update(
            {
                "udid": device.udid,
                "status": status,
                "provider": None,  # no provider indicate not present
                "properties": {
                    "name": device.name,
                    "brand": "Apple",
                    "model": device.product,
                    "version": device.version,
                    "serial": device.serial,
                },
            }
        )
    elif status == device.READY:
        logger.debug("{} {}", device, "healthcheck passed")

        assert isinstance(info, dict)
        info = defaultdict(dict, info)

        await hbc.device_update(
            {
                "udid": device.udid,
                "status": status,
                "provider": {
                    "wdaUrl": f"http://{current_ip()}:{device.public_port}"
                },
                "properties": {
                    "ip": info['value']['ios']['ip'],
                    "version": info['value']['os']['version'],
                    "sdkVersion": info['value']['os']['sdkVersion'],
                }
            }
        )  # yapf: disable
    elif status == device.FATAL:
        await hbc.device_update(
            {
                "udid": device.udid,
                "status": status,
                "provider": None,
            }
        )
    else:
        logger.error("{} unknown status: {}", device, status)


async def device_watch(wda_directory: str, manually_start_wda: bool, use_tidevice: bool, wda_bundle_pattern: bool):
    """
    When iOS device plugin, launch WDA
    """
    lock = locks.Lock()  # WDA launch one by one

    async for event in track_devices():
        if event.udid.startswith("ffffffffffffffffff"):
            logger.debug("Invalid event: {}", event)
            continue
        logger.debug("Event: {}", event)
        if event.present:
            device = WDADevice(event.udid, lock=lock, callback=_device_callback)
            device.wda_directory = wda_directory
            device.manually_start_wda = manually_start_wda
            device.use_tidevice = use_tidevice
            device.wda_bundle_pattern = wda_bundle_pattern
            udid2devices[event.udid] = device
            device.start()
        else:  # offline
            await udid2devices[event.udid].stop()
            udid2devices.pop(event.udid)


def stop_devices():
    for device in udid2devices.values():
        device.destroy()
    udid2devices.clear()


async def async_main():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument("-d", "--debug", action="store_true", help="enable debug mode")
    parser.add_argument("-p", "--port", type=int, default=3600, help="listen port")
    parser.add_argument(
        "-s",
        "--server",
        type=str,
        default="localhost:4000",
        required=False,
        help="server address",
    )
    parser.add_argument(
        "-W",
        "--wda-directory",
        default="./WebDriverAgent",
        help="WebDriverAgent source directory",
    )
    parser.add_argument(
        "--manually-start-wda",
        action="store_true",
        help="Start wda manually like using tidevice(with xctest). Then atx won't start WebDriverAgent",
    )
    parser.add_argument(
        "--use-tidevice",
        action="store_true",
        help="Start wda automatically using tidevice command. Only works when not using manually-start-wda",
    )
    parser.add_argument(
        "--wda-bundle-pattern",
        type=str,
        default="*WebDriverAgent*",
        required=False,
        help="If using --use-tidevice, can override wda bundle name pattern manually",
    )
    parser.add_argument(
        "--support-ios17",
        action="store_true",
        help="Start tunnel server for iOS 17+ devices using go-ios command",
    )

    args = parser.parse_args()

    setup_logger()
    register_signal_handler()

    # start server
    # enable_pretty_logging()
    app = make_app(debug=args.debug)
    app.listen(args.port)

    global listen_on
    listen_on = provider_url = f"http://{current_ip()}:{args.port}"
    logger.info("Starting server on {}", listen_on)

    if args.support_ios17:
        start_tunnel_server()

    start_socat()

    global hbc
    hbc = await heartbeat.heartbeat_connect(args.server, platform="ios", self_url=provider_url)

    await device_watch(
        args.wda_directory,
        args.manually_start_wda,
        args.use_tidevice,
        args.wda_bundle_pattern,
    )


def setup_logger():
    log_path = basedir / "logs"
    if not log_path.is_dir():
        log_path.mkdir(mode=0o755, parents=True)

    # 删除loguru自动初始化的终端输出
    logger.remove()

    # 日志输出到终端
    logger.add(
        sys.stderr,
        level="DEBUG",
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
               "<level>{level: <8}</level> | "
               "<light-magenta>{thread.name}_{thread.id}</light-magenta> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        backtrace=True,
        diagnose=True,
    )

    # 日志输出到文件
    # logger.add(
    #     str(log_path / f"{basename}-{{time:YYYYMMDD}}.log"),
    #     level="INFO",
    #     format="{time:YYYY-MM-DD HH:mm:ss.SSS} | "
    #     "{level: <8} | "
    #     "{thread.name}_{thread.id} | "
    #     "{name}:{function}:{line} - {message}",
    #     backtrace=True,
    #     diagnose=True,
    #     enqueue=True,
    #     rotation="100 MB",
    #     retention="7 days",
    # )

    # logging.basicConfig(handlers=[InterceptHandler()], level=0)


def register_signal_handler():
    signal.signal(signal.SIGTERM, exit_signal_handler)
    signal.signal(signal.SIGINT, exit_signal_handler)


def exit_signal_handler(sig, frame):
    logger.info("caught a signal: <{}>", signal.strsignal(sig))
    IOLoop.current().add_callback(shutdown)


def shutdown():
    IOLoop.instance().stop()
    stop_socat()
    stop_devices()
    logger.info("Stopping server on {}", listen_on)


if __name__ == "__main__":
    try:
        IOLoop.current().run_sync(async_main)
    except asyncio.TimeoutError:
        pass
    except Exception as _e:
        logger.exception("got an exception: {}", _e)
